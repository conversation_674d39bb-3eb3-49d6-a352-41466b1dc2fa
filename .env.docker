# LlamaBot Docker Environment Configuration
# Copy this file to .env and fill in your actual values

# Required: OpenRouter API Key
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Model Configuration (OpenRouter format)
# Popular options: openai/gpt-4o-mini, openai/gpt-4o, anthropic/claude-3-5-sonnet,
# meta-llama/llama-3.1-8b-instruct, google/gemini-pro-1.5, mistralai/mistral-7b-instruct
MODEL_NAME=openai/gpt-4o-mini

# Optional: LangSmith Configuration (for observability)
LANGSMITH_API_KEY=your_langsmith_api_key_here
LANGSMITH_TRACING=true
LANGSMITH_ENDPOINT=https://api.smith.langchain.com
LANGSMITH_PROJECT=llamabot-docker

# Database Configuration (Production)
POSTGRES_USER=langgraph_user
POSTGRES_PASSWORD=your_secure_password_here
POSTGRES_DB=langgraph_prod

# Frontend Configuration
FRONTEND_API_URL=http://localhost

# Development vs Production
NODE_ENV=production
PYTHON_ENV=production
