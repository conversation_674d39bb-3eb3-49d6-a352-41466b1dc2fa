# LlamaBot Docker Setup Guide

This document provides a comprehensive overview of the Docker and Docker Compose setup for LlamaBot.

## 🐳 Files Created

### Core Docker Files
- `Dockerfile` - Production backend container
- `Dockerfile.dev` - Development backend container with hot reload
- `frontend/Dockerfile` - Production frontend container
- `frontend/Dockerfile.dev` - Development frontend container with hot reload

### Docker Compose Configurations
- `docker-compose.yml` - Default configuration
- `docker-compose.dev.yml` - Development with hot reload and debugging
- `docker-compose.prod.yml` - Production with Nginx reverse proxy

### Configuration Files
- `.env.docker` - Environment template with all variables
- `.dockerignore` - Backend Docker ignore rules
- `frontend/.dockerignore` - Frontend Docker ignore rules
- `init-db.sql` - PostgreSQL initialization script

### Nginx Configuration (Production)
- `nginx/nginx.conf` - Main Nginx configuration
- `nginx/conf.d/default.conf` - Server block configuration
- `frontend/nginx.conf` - Frontend-specific Nginx config

### Utility Scripts
- `scripts/setup.sh` - Initial project setup
- `scripts/start.sh` - Docker Compose management
- `scripts/quick-start.sh` - Interactive setup with model selection

## 🚀 Quick Start

### Super Quick (Interactive)
```bash
git clone https://github.com/KodyKendall/LlamaBot.git
cd LlamaBot
./scripts/quick-start.sh
```

### Manual Setup
```bash
git clone https://github.com/KodyKendall/LlamaBot.git
cd LlamaBot
./scripts/setup.sh
# Edit .env with your API keys
./scripts/start.sh dev
```

## 🔧 Environment Variables

### Required
- `OPENROUTER_API_KEY` - Your OpenRouter API key

### Model Configuration
- `MODEL_NAME` - OpenRouter model format (default: openai/gpt-4o-mini)

### Optional
- `LANGSMITH_API_KEY` - For observability
- `LANGSMITH_TRACING` - Enable/disable tracing
- `LANGSMITH_PROJECT` - Project name for LangSmith
- `POSTGRES_USER` - Database user (auto-configured)
- `POSTGRES_PASSWORD` - Database password

## 🎯 Available Modes

### Development Mode
- Hot reload for both backend and frontend
- Debug logging enabled
- Source code mounted as volumes
- PostgreSQL with persistent data

```bash
./scripts/start.sh dev up
```

### Production Mode
- Optimized builds
- Nginx reverse proxy
- Security headers
- Rate limiting
- Health checks

```bash
./scripts/start.sh prod up
```

## 🌐 Endpoints

After startup:
- **Simple Chat**: http://localhost:8000/chat
- **Modern React UI**: http://localhost:3001
- **Generated Content**: http://localhost:8000/page
- **API Docs**: http://localhost:8000/docs
- **Health Check**: http://localhost/health (prod mode)

## 🔄 Common Commands

```bash
# Start development environment
./scripts/start.sh dev up

# Stop services
./scripts/start.sh dev down

# View logs
./scripts/start.sh dev logs

# Rebuild containers
./scripts/start.sh dev build

# Production deployment
./scripts/start.sh prod up
```

## 🏗️ Architecture

### Development Stack
- **Backend**: FastAPI with hot reload
- **Frontend**: React + Vite dev server
- **Database**: PostgreSQL 15
- **Networking**: Bridge network for service communication

### Production Stack
- **Reverse Proxy**: Nginx with security headers
- **Backend**: FastAPI (optimized)
- **Frontend**: React (built + served by Nginx)
- **Database**: PostgreSQL 15 with persistent volumes
- **Security**: Rate limiting, CORS, security headers

## 🔒 Security Features

- Non-root users in containers
- Security headers (XSS, CSRF protection)
- Rate limiting on API endpoints
- CORS configuration
- Health checks for all services
- Secrets management via environment variables

## 📊 Monitoring & Debugging

- Health checks for all services
- Structured logging
- LangSmith integration for AI observability
- Docker Compose logs aggregation
- Volume mounts for development debugging

## 🚢 Deployment Options

### Cloud Platforms
- **AWS**: ECS, EKS, EC2
- **Google Cloud**: Cloud Run, GKE, Compute Engine
- **Azure**: Container Instances, AKS
- **DigitalOcean**: App Platform, Droplets
- **Railway/Render**: Direct Docker deployment

### Local Development
- Docker Desktop (Windows/Mac)
- Docker Engine (Linux)
- Podman (alternative to Docker)

## 🛠️ Customization

### Adding New Models
Edit `.env`:
```env
MODEL_NAME=anthropic/claude-3-5-sonnet
```

### Custom Nginx Configuration
Edit `nginx/conf.d/default.conf` for custom routing, SSL, etc.

### Database Configuration
Modify `init-db.sql` for custom database setup.

### Environment-Specific Overrides
Create `docker-compose.override.yml` for local customizations.

## 🐛 Troubleshooting

### Common Issues
1. **Port conflicts**: Change ports in docker-compose files
2. **Permission issues**: Ensure Docker daemon is running
3. **API key errors**: Verify OPENROUTER_API_KEY in .env
4. **Database connection**: Check PostgreSQL health status

### Debug Commands
```bash
# Check container status
docker-compose -f docker-compose.dev.yml ps

# View specific service logs
docker-compose -f docker-compose.dev.yml logs backend

# Execute commands in containers
docker-compose -f docker-compose.dev.yml exec backend bash

# Reset everything
docker-compose -f docker-compose.dev.yml down -v
docker system prune -f
```

## 📝 Notes

- All containers use non-root users for security
- Persistent volumes ensure data survives container restarts
- Development mode mounts source code for hot reload
- Production mode uses multi-stage builds for optimization
- Health checks ensure services are ready before dependencies start
