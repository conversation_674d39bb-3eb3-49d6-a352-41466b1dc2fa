version: '3.8'

services:
  # PostgreSQL Database (same as production)
  postgres:
    image: postgres:15-alpine
    container_name: llamabot-postgres-dev
    environment:
      POSTGRES_DB: langgraph_dev
      POSTGRES_USER: langgraph_user
      POSTGRES_PASSWORD: langgraph_pass
    volumes:
      - postgres_data_dev:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U langgraph_user -d langgraph_dev"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    networks:
      - llamabot-network

  # Backend Service (Development with hot reload)
  backend:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: llamabot-backend-dev
    environment:
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - MODEL_NAME=${MODEL_NAME:-openai/gpt-4o-mini}
      - LANGSMITH_API_KEY=${LANGSMITH_API_KEY:-}
      - LANGSMITH_TRACING=${LANGSMITH_TRACING:-true}
      - LANGSMITH_ENDPOINT=${LANGSMITH_ENDPOINT:-https://api.smith.langchain.com}
      - LANGSMITH_PROJECT=${LANGSMITH_PROJECT:-llamabot-dev}
      - DB_URI=********************************************************/langgraph_dev
      - PYTHONPATH=/app
    volumes:
      - .:/app
      - /app/venv
      - backend_logs_dev:/app/logs
    ports:
      - "8000:8000"
    depends_on:
      postgres:
        condition: service_healthy
    command: ["uvicorn", "backend.app:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
    restart: unless-stopped
    networks:
      - llamabot-network

  # Frontend Service (Development with hot reload)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: llamabot-frontend-dev
    environment:
      - VITE_API_URL=http://localhost:8000
    volumes:
      - ./frontend:/app
      - /app/node_modules
    ports:
      - "3001:3001"
    depends_on:
      - backend
    command: ["npm", "run", "dev", "--", "--host", "0.0.0.0"]
    restart: unless-stopped
    networks:
      - llamabot-network

volumes:
  postgres_data_dev:
    driver: local
  backend_logs_dev:
    driver: local

networks:
  llamabot-network:
    driver: bridge
