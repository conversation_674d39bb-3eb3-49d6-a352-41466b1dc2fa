OPENROUTER_API_KEY=sk-or-v1-

# Model Configuration (OpenRouter format)
MODEL_NAME=deepseek/deepseek-r1-0528:free

# (Optional - LangSmith is optional if you want observability)
LANGSMITH_API_KEY=lsv2_pt_
LANGSMITH_TRACING=true
LANGSMITH_ENDPOINT="https://api.smith.langchain.com"
LANGSMITH_PROJECT=llamabot-open-source

# (Optional to use PSQL for long-term chat & agent state storage)
DB_URI=postgresql://langgraph_user:langgraph_pass@localhost:5432/langgraph_dev