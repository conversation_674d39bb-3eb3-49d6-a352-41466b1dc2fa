#!/usr/bin/env python3
"""
Test script to verify OpenRouter integration with LangChain
"""
import os
import sys
from dotenv import load_dotenv

# Add the backend directory to the path
sys.path.append('backend')

def test_base_agent():
    """Test the BaseAgent with OpenRouter configuration"""
    try:
        from agents.base_agent import BaseAgent
        
        # Create a concrete implementation for testing
        class TestAgent(BaseAgent):
            def run(self, input: str) -> str:
                return self.invoke(input)
        
        # Initialize the agent
        agent = TestAgent("Test Agent", "A test agent for OpenRouter integration")
        
        # Test a simple invocation
        response = agent.invoke("Hello! Please respond with 'OpenRouter integration working'")
        print(f"✅ BaseAgent test successful!")
        print(f"Response: {response}")
        return True
        
    except Exception as e:
        print(f"❌ BaseAgent test failed: {e}")
        return False

def test_react_agent():
    """Test the React Agent configuration"""
    try:
        from agents.react_agent.nodes import software_developer_assistant
        from langgraph.graph import MessagesState
        from langchain_core.messages import HumanMessage
        
        # Create a test state
        state = MessagesState(messages=[HumanMessage(content="Hello, test message")])
        
        # Test the software developer assistant
        result = software_developer_assistant(state)
        print(f"✅ React Agent test successful!")
        print(f"Response type: {type(result)}")
        return True
        
    except Exception as e:
        print(f"❌ React Agent test failed: {e}")
        return False

def check_environment():
    """Check if required environment variables are set"""
    load_dotenv()
    
    openrouter_key = os.getenv("OPENROUTER_API_KEY")
    if not openrouter_key:
        print("❌ OPENROUTER_API_KEY not found in environment variables")
        print("Please set your OpenRouter API key in the .env file")
        return False
    
    if openrouter_key == "your_openrouter_api_key_here":
        print("❌ OPENROUTER_API_KEY is still set to placeholder value")
        print("Please update your .env file with a real OpenRouter API key")
        return False
    
    print("✅ Environment variables configured correctly")
    return True

def main():
    """Main test function"""
    print("🧪 Testing OpenRouter Integration with LangChain")
    print("=" * 50)
    
    # Check environment first
    if not check_environment():
        print("\n❌ Environment check failed. Please configure your .env file.")
        return
    
    print("\n🔧 Testing BaseAgent...")
    base_agent_success = test_base_agent()
    
    print("\n🔧 Testing React Agent...")
    react_agent_success = test_react_agent()
    
    print("\n" + "=" * 50)
    if base_agent_success and react_agent_success:
        print("🎉 All tests passed! OpenRouter integration is working correctly.")
    else:
        print("⚠️  Some tests failed. Please check the error messages above.")

if __name__ == "__main__":
    main()
