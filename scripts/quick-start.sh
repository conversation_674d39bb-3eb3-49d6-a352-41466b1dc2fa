#!/bin/bash

# LlamaBot Quick Start Script
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "🦙 LlamaBot Quick Start"
echo "======================"
echo ""

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

# Run setup if needed
if [ ! -f .env ]; then
    print_status "Running initial setup..."
    ./scripts/setup.sh
fi

# Prompt for API key if not set
if ! grep -q "OPENROUTER_API_KEY=sk-" .env 2>/dev/null; then
    echo ""
    print_warning "OpenRouter API key not found in .env file."
    echo "You can get a free API key at: https://openrouter.ai/"
    echo ""
    read -p "Enter your OpenRouter API key (or press Enter to skip): " api_key
    
    if [ ! -z "$api_key" ]; then
        # Update the .env file with the API key
        sed -i.bak "s/OPENROUTER_API_KEY=your_openrouter_api_key_here/OPENROUTER_API_KEY=$api_key/" .env
        print_success "API key updated in .env file"
    else
        print_warning "Skipping API key setup. You'll need to edit .env manually."
    fi
fi

# Prompt for model selection
echo ""
print_status "Current model: $(grep MODEL_NAME .env | cut -d'=' -f2)"
echo ""
echo "Popular model options:"
echo "1. openai/gpt-4o-mini (default, fast and cheap)"
echo "2. openai/gpt-4o (more capable, higher cost)"
echo "3. anthropic/claude-3-5-sonnet (excellent for coding)"
echo "4. meta-llama/llama-3.1-8b-instruct (open source)"
echo "5. Keep current model"
echo ""
read -p "Select a model (1-5) or press Enter for default: " model_choice

case $model_choice in
    1)
        sed -i.bak "s/MODEL_NAME=.*/MODEL_NAME=openai\/gpt-4o-mini/" .env
        print_success "Model set to openai/gpt-4o-mini"
        ;;
    2)
        sed -i.bak "s/MODEL_NAME=.*/MODEL_NAME=openai\/gpt-4o/" .env
        print_success "Model set to openai/gpt-4o"
        ;;
    3)
        sed -i.bak "s/MODEL_NAME=.*/MODEL_NAME=anthropic\/claude-3-5-sonnet/" .env
        print_success "Model set to anthropic/claude-3-5-sonnet"
        ;;
    4)
        sed -i.bak "s/MODEL_NAME=.*/MODEL_NAME=meta-llama\/llama-3.1-8b-instruct/" .env
        print_success "Model set to meta-llama/llama-3.1-8b-instruct"
        ;;
    5|"")
        print_status "Keeping current model configuration"
        ;;
    *)
        print_warning "Invalid selection. Keeping current model configuration"
        ;;
esac

# Start the application
echo ""
print_status "Starting LlamaBot in development mode..."
./scripts/start.sh dev up

echo ""
print_success "🎉 LlamaBot is now running!"
echo ""
echo "Available endpoints:"
echo "- Simple Chat Interface: http://localhost:8000/chat"
echo "- Modern React Interface: http://localhost:3001"
echo "- Generated Content: http://localhost:8000/page"
echo "- API Documentation: http://localhost:8000/docs"
echo ""
echo "To stop: ./scripts/start.sh dev down"
echo "To view logs: ./scripts/start.sh dev logs"
