#!/bin/bash

# LlamaBot Docker Startup Script
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if Docker Compose is available
if ! command -v docker-compose > /dev/null 2>&1; then
    print_error "Docker Compose is not installed. Please install Docker Compose and try again."
    exit 1
fi

# Check for environment file
if [ ! -f .env ]; then
    print_warning "No .env file found. Creating from template..."
    cp .env.docker .env
    print_warning "Please edit .env file with your actual API keys before running again."
    exit 1
fi

# Parse command line arguments
MODE=${1:-dev}
COMMAND=${2:-up}

case $MODE in
    "dev"|"development")
        COMPOSE_FILE="docker-compose.dev.yml"
        print_status "Starting LlamaBot in development mode..."
        ;;
    "prod"|"production")
        COMPOSE_FILE="docker-compose.prod.yml"
        print_status "Starting LlamaBot in production mode..."
        ;;
    *)
        COMPOSE_FILE="docker-compose.yml"
        print_status "Starting LlamaBot in default mode..."
        ;;
esac

# Execute Docker Compose command
case $COMMAND in
    "up")
        print_status "Building and starting services..."
        docker-compose -f $COMPOSE_FILE up --build -d
        print_success "Services started successfully!"
        print_status "Backend available at: http://localhost:8000"
        print_status "Frontend available at: http://localhost:3001"
        print_status "Simple chat interface: http://localhost:8000/chat"
        ;;
    "down")
        print_status "Stopping services..."
        docker-compose -f $COMPOSE_FILE down
        print_success "Services stopped successfully!"
        ;;
    "logs")
        docker-compose -f $COMPOSE_FILE logs -f
        ;;
    "build")
        print_status "Building services..."
        docker-compose -f $COMPOSE_FILE build
        print_success "Build completed successfully!"
        ;;
    *)
        print_status "Running custom command: $COMMAND"
        docker-compose -f $COMPOSE_FILE $COMMAND
        ;;
esac
