#!/bin/bash

# LlamaBot Docker Setup Script
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_status "Setting up LlamaBot Docker environment..."

# Create necessary directories
print_status "Creating necessary directories..."
mkdir -p nginx/conf.d
mkdir -p nginx/ssl
mkdir -p logs
mkdir -p data/postgres

# Create page.html if it doesn't exist
if [ ! -f page.html ]; then
    print_status "Creating initial page.html..."
    cat > page.html << 'EOF'
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LlamaBot Generated Content</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .container {
            text-align: center;
            padding: 50px 20px;
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        p {
            color: #666;
            font-size: 18px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Welcome to LlamaBot!</h1>
        <p>This page will be updated with generated content from your conversations.</p>
        <p>Start chatting to see your creations appear here!</p>
    </div>
</body>
</html>
EOF
fi

# Set up environment file
if [ ! -f .env ]; then
    print_status "Creating environment file from template..."
    cp .env.docker .env
    print_warning "Please edit .env file with your actual configuration!"
    print_warning "Required: OPENROUTER_API_KEY"
    print_warning "Optional: MODEL_NAME (defaults to openai/gpt-4o-mini)"
    print_warning "Optional: LANGSMITH_API_KEY for observability"
else
    print_success "Environment file already exists."
fi

# Make scripts executable
print_status "Making scripts executable..."
chmod +x scripts/*.sh

print_success "Setup completed successfully!"
print_status "Next steps:"
echo "1. Edit .env file with your API keys"
echo "2. Run: ./scripts/start.sh dev (for development)"
echo "3. Run: ./scripts/start.sh prod (for production)"
echo ""
print_status "Available endpoints after startup:"
echo "- Backend API: http://localhost:8000"
echo "- Frontend (React): http://localhost:3001"
echo "- Simple Chat: http://localhost:8000/chat"
echo "- Generated Content: http://localhost:8000/page"
