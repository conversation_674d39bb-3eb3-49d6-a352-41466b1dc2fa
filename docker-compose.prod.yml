version: '3.8'

services:
  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: llamabot-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - backend
      - frontend
    restart: unless-stopped
    networks:
      - llamabot-network

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: llamabot-postgres-prod
    environment:
      POSTGRES_DB: langgraph_prod
      POSTGRES_USER: ${POSTGRES_USER:-langgraph_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-langgraph_pass}
    volumes:
      - postgres_data_prod:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-langgraph_user} -d langgraph_prod"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    networks:
      - llamabot-network

  # Backend Service
  backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: llamabot-backend-prod
    environment:
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - MODEL_NAME=${MODEL_NAME:-openai/gpt-4o-mini}
      - LANGSMITH_API_KEY=${LANGSMITH_API_KEY:-}
      - LANGSMITH_TRACING=${LANGSMITH_TRACING:-false}
      - LANGSMITH_ENDPOINT=${LANGSMITH_ENDPOINT:-https://api.smith.langchain.com}
      - LANGSMITH_PROJECT=${LANGSMITH_PROJECT:-llamabot-prod}
      - DB_URI=postgresql://${POSTGRES_USER:-langgraph_user}:${POSTGRES_PASSWORD:-langgraph_pass}@postgres:5432/langgraph_prod
      - PYTHONPATH=/app
    volumes:
      - ./page.html:/app/page.html
      - ./assets:/app/assets
      - backend_logs_prod:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped
    networks:
      - llamabot-network

  # Frontend Service
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: llamabot-frontend-prod
    environment:
      - VITE_API_URL=${FRONTEND_API_URL:-http://localhost}
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3001/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped
    networks:
      - llamabot-network

volumes:
  postgres_data_prod:
    driver: local
  backend_logs_prod:
    driver: local

networks:
  llamabot-network:
    driver: bridge
