# Git
.git
.gitignore

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Environment files
.env
.env.*

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/
chat_app.log

# Node modules (handled separately in frontend)
node_modules/

# Temporary files
*.tmp
*.temp

# Documentation
docs/
README.md
LICENSE

# Examples and experiments
examples/
experiments/

# Test files
test_*
*_test.py
tests/
