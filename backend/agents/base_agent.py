from abc import ABC, abstractmethod
import os

from langchain_openai import ChatOpenAI
from dotenv import load_dotenv

class BaseAgent(ABC):
    def __init__(self, name: str, description: str):
        self.name = name
        self.description = description

        load_dotenv()
        # Configure ChatOpenAI to use OpenRouter
        self.llm = ChatOpenAI(
            model=os.getenv("MODEL_NAME", "openai/gpt-4o-mini"),  # OpenRouter model format
            api_key=os.getenv("OPENROUTER_API_KEY"),
            base_url="https://openrouter.ai/api/v1"
        )

    @abstractmethod
    def run(self, input: str) -> str:
        pass

    def invoke(self, input: str) -> str:
        return self.llm.invoke(input)
