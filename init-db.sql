-- Initialize LlamaBot Database
-- This script sets up the initial database structure for LlamaBot

-- Create the database (this is handled by POSTGRES_DB environment variable)
-- But we can add additional setup here

-- Grant additional permissions to ensure <PERSON><PERSON><PERSON><PERSON> can create tables
GRANT ALL PRIVILEGES ON DATABASE langgraph_dev TO langgraph_user;

-- Connect to the database
\c langgraph_dev;

-- <PERSON> schema permissions
GRANT USAGE ON SCHEMA public TO langgraph_user;
GRANT CREATE ON SCHEMA public TO langgraph_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO langgraph_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO langgraph_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO langgraph_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO langgraph_user;

-- Create extension for UUID generation if needed
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Log successful initialization
SELECT 'LlamaBot database initialized successfully' as status;
