# Development Dockerfile for LlamaBot Frontend
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Install dependencies for development
RUN apk add --no-cache git

# Copy package files
COPY package*.json ./

# Install all dependencies (including dev dependencies)
RUN npm install

# Expose port
EXPOSE 3001

# Command to run the development server
CMD ["npm", "run", "dev", "--", "--host", "0.0.0.0"]
