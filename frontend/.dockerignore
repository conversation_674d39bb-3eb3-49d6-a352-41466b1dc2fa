# Git
.git
.gitignore

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Environment files
.env
.env.*

# Node
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Build outputs
dist/
build/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Temporary files
*.tmp
*.temp

# Documentation
README.md

# Test files
coverage/
.nyc_output/
