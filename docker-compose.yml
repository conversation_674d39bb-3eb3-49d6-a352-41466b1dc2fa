version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: llamabot-postgres
    environment:
      POSTGRES_DB: langgraph_dev
      POSTGRES_USER: langgraph_user
      POSTGRES_PASSWORD: langgraph_pass
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U langgraph_user -d langgraph_dev"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    networks:
      - llamabot-network

  # Backend Service
  backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: llamabot-backend
    environment:
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - MODEL_NAME=${MODEL_NAME:-deepseek/deepseek-r1-0528:free}
      - LANGSMITH_API_KEY=${LANGSMITH_API_KEY:-}
      - LANGSMITH_TRACING=${LANGSMITH_TRACING:-false}
      - LANGSMITH_ENDPOINT=${LANGSMITH_ENDPOINT:-https://api.smith.langchain.com}
      - LANGSMITH_PROJECT=${LANGSMITH_PROJECT:-llamabot-docker}
      - DB_URI=********************************************************/langgraph_dev
      - PYTHONPATH=/app
    volumes:
      - ./page.html:/app/page.html
      - ./assets:/app/assets
      - backend_logs:/app/logs
    ports:
      - "8000:8000"
    depends_on:
      postgres:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped
    networks:
      - llamabot-network

  # Frontend Service (React)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: llamabot-frontend
    environment:
      - VITE_API_URL=http://localhost:8000
    ports:
      - "3001:3001"
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3001/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped
    networks:
      - llamabot-network

volumes:
  postgres_data:
    driver: local
  backend_logs:
    driver: local

networks:
  llamabot-network:
    driver: bridge
